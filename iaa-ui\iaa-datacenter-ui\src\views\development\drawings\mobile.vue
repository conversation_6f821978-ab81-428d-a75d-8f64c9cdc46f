<template>
  <div class="mobile-drawings-container">
    <!-- 顶部搜索栏 -->
    <van-sticky>
      <div class="search-header">
        <van-search
          v-model="searchValue"
          placeholder="请输入类型名称搜索"
          @search="onSearch"
          @clear="onClear"
        />
      </div>
    </van-sticky>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 分类选择器 -->
      <van-collapse v-model="activeNames" accordion>
        <van-collapse-item title="选择分类" name="category">
          <div class="category-tree">
            <van-tree-select
              v-model:active-id="activeCategory"
              v-model:main-active-index="activeIndex"
              :items="treeSelectItems"
              @click-nav="onClickNav"
              @click-item="onClickItem"
            />
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 物料列表 -->
      <div class="material-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <van-card
              v-for="item in list"
              :key="item.itemCode"
              :title="item.itemName"
              :desc="item.spec"
              :thumb="getItemIcon(item.attribute)"
              @click="handleItemClick(item)"
            >
              <template #tags>
                <van-tag type="primary" size="mini">{{ item.itemCode }}</van-tag>
                <van-tag type="success" size="mini" v-if="item.itemVersion">
                  v{{ item.itemVersion }}
                </van-tag>
              </template>
              <template #footer>
                <van-button size="mini" type="primary" @click.stop="viewDetails(item)">
                  查看详情
                </van-button>
              </template>
            </van-card>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 详情弹出层 -->
    <van-popup
      v-model:show="showDetail"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
    >
      <div class="detail-container">
        <div class="detail-header">
          <h3>{{ selectedItem?.itemName }}</h3>
          <p class="item-code">编码: {{ selectedItem?.itemCode }}</p>
        </div>
        
        <van-tabs v-model:active="activeTab">
          <van-tab title="基本信息">
            <div class="basic-info">
              <van-cell-group>
                <van-cell title="物料编码" :value="selectedItem?.itemCode" />
                <van-cell title="原件号" :value="selectedItem?.itemCode1" />
                <van-cell title="版本" :value="selectedItem?.itemVersion" />
                <van-cell title="物料名称" :value="selectedItem?.itemName" />
                <van-cell title="属性" :value="selectedItem?.attribute" />
                <van-cell title="规格" :value="selectedItem?.spec" />
              </van-cell-group>
            </div>
          </van-tab>
          
          <van-tab title="相关文档">
            <div class="document-list">
              <van-loading v-if="rightLoading" />
              <van-empty v-else-if="!rightList.length" description="暂无相关文档" />
              <div v-else>
                <van-card
                  v-for="doc in rightList"
                  :key="doc.documentCode"
                  :title="`${doc.documentName}.${doc.documentType}`"
                  :desc="doc.documentCode"
                  @click="handleDocClick(doc)"
                >
                  <template #tags>
                    <van-tag 
                      :type="doc.status.includes('完成') ? 'success' : 'danger'" 
                      size="mini"
                    >
                      {{ doc.status }}
                    </van-tag>
                    <van-tag 
                      :type="doc.isSuance.includes('已发放') ? 'success' : 'warning'" 
                      size="mini"
                    >
                      {{ doc.isSuance }}
                    </van-tag>
                    <van-tag 
                      :type="doc.format === '存在' ? 'success' : 'warning'" 
                      size="mini"
                    >
                      {{ doc.format }}PDF
                    </van-tag>
                  </template>
                  <template #footer>
                    <van-button 
                      size="mini" 
                      type="primary" 
                      @click.stop="previewDocument(doc)"
                      :disabled="!canPreview(doc)"
                    >
                      预览
                    </van-button>
                  </template>
                </van-card>
              </div>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </van-popup>

    <!-- PDF预览弹出层 -->
    <van-popup
      v-model:show="showPdfPreview"
      position="center"
      :style="{ width: '95%', height: '90%' }"
      round
      closeable
    >
      <div class="pdf-preview-container">
        <div class="pdf-header">
          <h4>{{ documentName }}</h4>
          <van-button 
            size="mini" 
            type="primary" 
            icon="down" 
            @click="handleDownload"
          >
            下载
          </van-button>
        </div>
        <div class="pdf-content">
          <pdf-preview :pdfUrl="pdfPreviewUrl" />
        </div>
      </div>
    </van-popup>

    <!-- 悬浮操作按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="eye-o"
      @click="showLogPanel = true"
    />

    <!-- 日志弹出层 -->
    <van-popup
      v-model:show="showLogPanel"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
    >
      <div class="log-container">
        <h3>操作日志</h3>
        <DrawingsLog />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { DrawingApi } from '@/api/development/drawings/index'
import { handleTree } from '@/utils/tree'
import PdfPreview from './componets/PdfPreview.vue'
import DrawingsLog from './componets/DrawingsLog.vue'
import { downloadByUrlBolb } from '@/utils/filt'
import { showToast, showConfirmDialog } from 'vant'

// 搜索相关
const searchValue = ref('')
const activeNames = ref(['category'])

// 分类选择相关
const activeCategory = ref('')
const activeIndex = ref(0)
const treeSelectItems = ref([])

// 列表相关
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const list = ref<any[]>([])
const total = ref(0)

// 详情相关
const showDetail = ref(false)
const selectedItem = ref(null)
const activeTab = ref(0)
const rightLoading = ref(false)
const rightList = ref<any[]>([])

// PDF预览相关
const showPdfPreview = ref(false)
const pdfPreviewUrl = ref('')
const documentName = ref('')
const documentCode = ref('')

// 日志相关
const showLogPanel = ref(false)

// 查询参数
const queryParams = reactive({
  kind: [] as string[],
  pageNo: 1,
  pageSize: 20
})

const queryRightParams = reactive({
  itemCode: '',
  itemVersion: ''
})

// 获取物料图标
const getItemIcon = (attribute: string) => {
  // 根据属性返回不同的图标
  const iconMap = {
    '零件': 'https://img.yzcdn.cn/vant/cat.jpeg',
    '组件': 'https://img.yzcdn.cn/vant/cat.jpeg',
    '产品': 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
  return iconMap[attribute] || 'https://img.yzcdn.cn/vant/cat.jpeg'
}

// 搜索功能
const onSearch = (val: string) => {
  console.log('搜索:', val)
  // 实现搜索逻辑
}

const onClear = () => {
  searchValue.value = ''
}

// 下拉刷新
const onRefresh = () => {
  queryParams.pageNo = 1
  finished.value = false
  getList().finally(() => {
    refreshing.value = false
  })
}

// 加载更多
const onLoad = () => {
  if (finished.value) return
  
  queryParams.pageNo++
  getList()
}

// 分类选择
const onClickNav = (index: number) => {
  activeIndex.value = index
}

const onClickItem = (item: any) => {
  activeCategory.value = item.id
  if (item.id === '1') {
    queryParams.kind = []
  } else {
    queryParams.kind = [item.id]
  }
  queryParams.pageNo = 1
  finished.value = false
  list.value = []
  getList()
}

// 物料项点击
const handleItemClick = (item: any) => {
  selectedItem.value = item
  queryRightParams.itemCode = item.itemCode
  queryRightParams.itemVersion = item.itemVersion
  getRightList()
  showDetail.value = true
}

const viewDetails = (item: any) => {
  handleItemClick(item)
}

// 文档点击
const handleDocClick = (doc: any) => {
  console.log('文档点击:', doc)
}

// 预览文档
const previewDocument = async (doc: any) => {
  if (!canPreview(doc)) {
    showToast('文档无法预览')
    return
  }

  try {
    rightLoading.value = true
    const res = await DrawingApi.getDocPathOrWx({
      methodName: 'getDocPathForWX',
      docId: doc.documentCode,
      docVer: doc.documentVersion,
      docName: doc.documentName,
      uuid: '1'
    })

    if (res && typeof res === 'string') {
      pdfPreviewUrl.value = res + '#printbar=0'
      documentName.value = doc.documentName
      documentCode.value = doc.documentCode
      showPdfPreview.value = true

      // 记录预览日志
      await DrawingApi.addDrawingLog({
        itemCode: queryRightParams.itemCode,
        itemVersion: queryRightParams.itemVersion,
        wordName: doc.documentName,
        wordCode: doc.documentCode,
        type: 0
      })
    } else {
      showToast('获取文件地址失败')
    }
  } catch (error) {
    console.error('预览失败:', error)
    showToast('预览失败，请重试')
  } finally {
    rightLoading.value = false
  }
}

// 判断是否可以预览
const canPreview = (doc: any) => {
  return doc.format === '存在' && doc.isSuance === '已发放' && doc.status === '受控完成'
}

// 下载文档
const handleDownload = async () => {
  try {
    await DrawingApi.addDrawingLog({
      itemCode: queryRightParams.itemCode,
      itemVersion: queryRightParams.itemVersion,
      wordName: documentName.value,
      wordCode: documentCode.value,
      type: 1
    })

    downloadByUrlBolb({
      url: pdfPreviewUrl.value,
      fileName: documentName.value + '.pdf'
    })

    showToast(`文档 "${documentName.value}" 开始下载`)
  } catch (error) {
    showToast('下载失败')
  }
}

// 获取树形数据
const getTreeList = async () => {
  try {
    const res = await DrawingApi.getQueryPartKind()
    const tree = handleTree(res)
    const filteredTree = tree.filter((item) => item.id == '1')

    // 转换为 van-tree-select 需要的格式
    treeSelectItems.value = convertToTreeSelectFormat(filteredTree)
  } catch (error) {
    console.error('获取分类失败:', error)
    showToast('获取分类失败')
  }
}

// 转换树形数据格式
const convertToTreeSelectFormat = (tree: any[]) => {
  return tree.map(item => ({
    text: item.kindName,
    id: item.id,
    children: item.children ? convertToTreeSelectFormat(item.children) : []
  }))
}

// 获取物料列表
const getList = async () => {
  try {
    loading.value = true
    const res = await DrawingApi.getPage(queryParams)

    if (queryParams.pageNo === 1) {
      list.value = res.list
    } else {
      list.value.push(...res.list)
    }

    total.value = res.total

    // 判断是否还有更多数据
    if (list.value.length >= total.value) {
      finished.value = true
    }
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败')
  } finally {
    loading.value = false
  }
}

// 获取右侧文档列表
const getRightList = async () => {
  try {
    rightLoading.value = true
    const res = await DrawingApi.getQueryDocumentByItemCode(queryRightParams)
    rightList.value = res
  } catch (error) {
    console.error('查询文档失败:', error)
    showToast('查询文档失败')
  } finally {
    rightLoading.value = false
  }
}

// 监听搜索值变化
watch(searchValue, (val) => {
  // 实现搜索过滤逻辑
  if (val) {
    // 可以在这里实现本地搜索或调用API搜索
  }
})

// 组件挂载时初始化
onMounted(async () => {
  await getTreeList()
  await getList()
})
</script>

<style scoped>
.mobile-drawings-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.search-header {
  background: white;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-content {
  padding: 8px;
}

.category-tree {
  max-height: 300px;
  overflow-y: auto;
}

.material-list {
  margin-top: 8px;
}

.material-list .van-card {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.detail-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  margin-bottom: 16px;
  text-align: center;
}

.detail-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.item-code {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.basic-info {
  padding: 16px 0;
}

.document-list {
  padding: 16px 0;
  max-height: 400px;
  overflow-y: auto;
}

.document-list .van-card {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.pdf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.pdf-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  flex: 1;
  margin-right: 16px;
}

.pdf-content {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  background: #f5f5f5;
}

.log-container {
  padding: 16px;
  height: 100%;
}

.log-container h3 {
  margin: 0 0 16px 0;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .search-header {
    padding: 4px;
  }

  .main-content {
    padding: 4px;
  }

  .detail-container {
    padding: 12px;
  }

  .pdf-preview-container {
    padding: 12px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-drawings-container {
    background-color: #1a1a1a;
  }

  .search-header {
    background: #2d2d2d;
  }

  .detail-header h3 {
    color: #fff;
  }

  .item-code {
    color: #999;
  }
}
</style>
